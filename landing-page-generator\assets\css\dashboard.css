/* Dashboard Styles */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');

* {
    font-family: 'Cairo', sans-serif !important;
}

body {
    background-color: #f8f9fa;
    direction: rtl;
}

.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.sidebar .nav-link {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    border-radius: 5px;
    margin: 2px 10px;
    padding: 10px 15px;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateX(-5px);
}

.sidebar .nav-link i {
    margin-left: 10px;
    width: 20px;
    text-align: center;
}

main {
    margin-right: 240px;
    padding: 20px;
}

@media (max-width: 768px) {
    .sidebar {
        position: relative;
        height: auto;
        padding: 20px 0;
    }
    
    main {
        margin-right: 0;
    }
}

.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0 !important;
    border: none;
    padding: 20px;
}

.card-header h3 {
    margin: 0;
    font-weight: 600;
}

.card-body {
    padding: 30px;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn {
    border-radius: 10px;
    padding: 12px 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    border: none;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(86, 171, 47, 0.3);
}

.btn-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border: none;
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(240, 147, 251, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #a8a8a8 0%, #d3d3d3 100%);
    border: none;
}

.nav-tabs {
    border-bottom: 2px solid #e9ecef;
}

.nav-tabs .nav-link {
    border: none;
    border-radius: 10px 10px 0 0;
    color: #6c757d;
    font-weight: 600;
    margin-left: 5px;
    padding: 15px 20px;
}

.nav-tabs .nav-link.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

.tab-content {
    background: white;
    border-radius: 0 15px 15px 15px;
    padding: 30px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.image-upload-container {
    border: 2px dashed #e9ecef;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.image-upload-container:hover {
    border-color: #667eea;
    background-color: #f8f9ff;
}

.image-preview {
    max-width: 100px;
    max-height: 100px;
    border-radius: 10px;
    margin: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.review-item {
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.review-item:hover {
    border-color: #667eea;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.1);
}

.review-item.selected {
    border-color: #667eea;
    background-color: #f8f9ff;
}

.content-section {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.alert {
    border-radius: 10px;
    border: none;
    padding: 15px 20px;
}

.alert-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    color: white;
}

.alert-danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    color: white;
}

.progress {
    height: 10px;
    border-radius: 10px;
    background-color: #e9ecef;
}

.progress-bar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
}

.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
    border: none;
}

.form-floating {
    margin-bottom: 20px;
}

.form-floating > .form-control {
    height: calc(3.5rem + 2px);
    line-height: 1.25;
}

.form-floating > label {
    padding: 1rem 0.75rem;
    color: #6c757d;
}

.image-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.image-item {
    position: relative;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 10px;
    text-align: center;
}

.image-item img {
    max-width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 5px;
}

.image-item .remove-btn {
    position: absolute;
    top: 5px;
    left: 5px;
    background: #ff4b2b;
    color: white;
    border: none;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    font-size: 12px;
    cursor: pointer;
}

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.status-badge {
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
}

.status-draft {
    background-color: #ffc107;
    color: #212529;
}

.status-published {
    background-color: #28a745;
    color: white;
}

.status-archived {
    background-color: #6c757d;
    color: white;
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .sidebar {
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    main {
        margin-right: 0;
    }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Form validation styles */
.is-invalid {
    border-color: #dc3545;
}

.invalid-feedback {
    display: block;
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 5px;
}

/* Tooltip styles */
.tooltip {
    font-size: 12px;
}

.tooltip-inner {
    background-color: #333;
    border-radius: 5px;
}

/* Animation for form sections */
.form-section {
    transition: all 0.3s ease;
}

.form-section.collapsed {
    max-height: 0;
    overflow: hidden;
    opacity: 0;
}

.form-section.expanded {
    max-height: 1000px;
    opacity: 1;
}
