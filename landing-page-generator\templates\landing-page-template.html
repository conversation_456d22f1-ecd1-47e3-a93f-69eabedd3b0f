<!DOCTYPE html>
<html dir="rtl">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="user-scalable=no, width=device-width, initial-scale=1, maximum-scale=1" />
    <meta name="theme-color" content="#FFF" />
    <meta name="msapplication-navbutton-color" content="#FFF" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="csrf-token" content="233G2Xj9p4pUeikewMtF9sgdnKzTDR772M94ZrfE">
    <title>{{TITLE}}</title>
    <meta property="og:title" content="{{TITLE}}" />
    <meta property="og:image" content="images/logo.png" />

    <meta name="twitter:title" content="{{TITLE}}" />
    <meta name="twitter:description" content="" />
    <meta name="twitter:images0" content="images/logo.png" />
    <link rel="icon" type="image/png" href="images/favicon.png" />
    
    <!-- Google Fonts Link For Icons -->
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@48,400,0,0" />

    <link href="css/root.css" rel="stylesheet">
    <link href="css/store_font.css" rel="stylesheet">

    <!-- Facebook Pixel Code -->
    <script>
        !function (f, b, e, v, n, t, s) {
            if (f.fbq) return; n = f.fbq = function () {
                n.callMethod ?
                n.callMethod.apply(n, arguments) : n.queue.push(arguments)
            };
            if (!f._fbq) f._fbq = n; n.push = n; n.loaded = !0; n.version = '2.0';
            n.queue = []; t = b.createElement(e); t.async = !0;
            t.src = v; s = b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t, s)
        }(window, document, 'script',
            'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', '{{FACEBOOK_PIXEL}}');
        fbq('track', 'PageView');
    </script>
    <noscript><img height="1" width="1" style="display:none"
            src="https://www.facebook.com/tr?id={{FACEBOOK_PIXEL}}&ev=PageView&noscript=1" /></noscript>
    <!-- End Meta Pixel Code -->

    <!-- Tiktok Pixel Code -->
    <script>
        !function (w, d, t) {
            w.TiktokAnalyticsObject = t; var ttq = w[t] = w[t] || []; ttq.methods = ["page", "track", "identify", "instances", "debug", "on", "off", "once", "ready", "alias", "group", "enableCookie", "disableCookie"], ttq.setAndDefer = function (t, e) { t[e] = function () { t.push([e].concat(Array.prototype.slice.call(arguments, 0))) } }; for (var i = 0; i < ttq.methods.length; i++)ttq.setAndDefer(ttq, ttq.methods[i]); ttq.instance = function (t) { for (var e = ttq._i[t] || [], n = 0; n < ttq.methods.length; n++)ttq.setAndDefer(e, ttq.methods[n]); return e }, ttq.load = function (e, n) { var i = "https://analytics.tiktok.com/i18n/pixel/events.js"; ttq._i = ttq._i || {}, ttq._i[e] = [], ttq._i[e]._u = i, ttq._t = ttq._t || {}, ttq._t[e] = +new Date, ttq._o = ttq._o || {}, ttq._o[e] = n || {}; var o = document.createElement("script"); o.type = "text/javascript", o.async = !0, o.src = i + "?sdkid=" + e + "&lib=" + t; var a = document.getElementsByTagName("script")[0]; a.parentNode.insertBefore(o, a) };

            ttq.load('{{TIKTOK_PIXEL}}');
            ttq.page();
        }(window, document, 'ttq');
    </script>
    <!-- End Tiktok Pixel Code -->

    <script>
        // Disable right-click
        document.addEventListener('contextmenu', function (e) {
            e.preventDefault();
        });

        // Disable cut, copy, and paste
        document.addEventListener('cut', function (e) {
            e.preventDefault();
        });
        document.addEventListener('copy', function (e) {
            e.preventDefault();
        });
        document.addEventListener('paste', function (e) {
            e.preventDefault();
        });

        // Disable text selection
        document.addEventListener('selectstart', function (e) {
            e.preventDefault();
        });

        // Disable drag and drop
        document.addEventListener('dragstart', function (e) {
            e.preventDefault();
        });

        document.addEventListener('dragover', function (e) {
            e.preventDefault();
        });

        document.addEventListener('drop', function (e) {
            e.preventDefault();
        });
    </script>

    {{FORM_VALIDATION_SCRIPT}}

    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.4/css/all.css">
    <link rel="stylesheet" href="css/fontawesome/V4-shims.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="clients_reviews/nicepage.css" media="screen">
    <link rel="stylesheet" href="clients_reviews/Page-7.css" media="screen">

    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900;1000&family=Open+Sans:wght@600&display=swap');

        @font-face {
            font-family: 'Cairo';
            src: url('../fonts/cairo/cairo.ttf') format('truetype');
        }

        :root {
            font-size: 16px;
            text-align: center;
            font-family: 'Cairo', sans-serif !important;
        }

        * {
            font-family: 'Cairo', sans-serif !important;
        }

        body {
            color: #1a1a1a;
            background: #FFFFFF;
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;
            touch-action: pan-y;
        }

        .column-child section {
            width: 100%;
        }

        @media (max-width: 425px) {
            :root {
                font-size: 12px;
            }
        }

        .page-builder>section {
            display: flex !important;
            justify-content: center;
            max-width: 100%;
            margin-top: 0px;
            margin-right: 0px;
            margin-bottom: 0px;
            margin-left: 0px;
        }

        .mySlides {
            display: none
        }

        img {
            vertical-align: middle;
        }

        /* Slideshow container */
        .slideshow-container {
            max-width: 1000px;
            position: relative;
            margin: auto;
        }

        /* Next & previous buttons */
        .prev,
        .next {
            cursor: pointer;
            position: absolute;
            top: 50%;
            left: 90%;
            width: auto;
            padding: 16px;
            margin-top: -22px;
            color: white;
            font-weight: bold;
            font-size: 18px;
            transition: 0.6s ease;
            border-radius: 0 3px 3px 0;
            user-select: none;
        }

        /* Position the "next button" to the right */
        .next {
            right: 90%;
            border-radius: 3px 0 0 3px;
        }

        /* On hover, add a black background color with a little bit see-through */
        .prev:hover,
        .next:hover {
            background-color: rgba(0, 0, 0, 0.8);
        }

        /* Caption text */
        .text {
            color: #f2f2f2;
            font-size: 15px;
            padding: 8px 12px;
            position: absolute;
            bottom: 8px;
            width: 100%;
            text-align: center;
        }

        /* Number text (1/3 etc) */
        .numbertext {
            color: #f2f2f2;
            font-size: 12px;
            padding: 8px 12px;
            position: absolute;
            top: 0;
        }

        /* The dots/bullets/indicators */
        .dot {
            cursor: pointer;
            height: 15px;
            width: 15px;
            margin: 0 2px;
            background-color: #bbb;
            border-radius: 50%;
            display: inline-block;
            transition: background-color 0.6s ease;
        }

        .active,
        .dot:hover {
            background-color: #717171;
        }

        /* Fading animation */
        .fade {
            animation-name: fade;
            animation-duration: 1.5s;
        }

        @keyframes fade {
            from {
                opacity: .4
            }

            to {
                opacity: 1
            }
        }

        /* On smaller screens, decrease text size */
        @media only screen and (max-width: 300px) {

            .prev,
            .next,
            .text {
                font-size: 11px
            }
        }
    </style>
    
    <script class="u-script" type="text/javascript" src="clients_reviews/jquery-1.9.1.min.js" defer=""></script>
    <script class="u-script" type="text/javascript" src="clients_reviews/nicepage.js" defer=""></script>
    <script src="js/slider1.js"></script>
    <link rel="stylesheet" href="css/loader.css">
</head>

<body class="body_fixed">
    <div class="loader"></div>

    <div id="app">
        <cart-side-summary
            :initial-cart="{&quot;sub_total&quot;:0,&quot;count&quot;:0,&quot;one_page_checkout&quot;:false,&quot;created_at&quot;:&quot;2023-04-16T17:52:26.299861Z&quot;,&quot;items&quot;:{&quot;data&quot;:[]}}"></cart-side-summary>
        
        <!-- Toast messages -->
        <flash></flash>
        
        <!-- App content -->
        <main class="page-wrapper">
            <div class="page-builder">
                <section id="PBS-ouwn1661723616425" class="section page-section column-section PBS-ouwn1661723616425">
                    <div class="inner-container" style="padding: 5px 0;">
                        <div class="inner-column" data-pb-debug="margin padding">
                            <div class="column-child">
                                <section id="PBS-svr6ws1661723616425"
                                    class="section page-section title-section PBS-svr6ws1661723616425">
                                    <div class="inner-container">
                                        <div class="inner-title" data-pb-debug="margin padding">
                                            <div class="fr-view">
                                                <p class="title-section-content">
                                                    <span class="title-section-stylish"></span>
                                                    <a href="" style="border: none; font-weight: 700; font-size: 17px;"
                                                        target="_blank" class="title-section-text">
                                                        {{MAIN_TITLE}}
                                                    </a>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </section>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Slideshow -->
                <div class="slideshow-container">
                    {{SLIDER_IMAGES}}
                    
                    <a class="prev" onclick="plusSlides(-1)">❮</a>
                    <a class="next" onclick="plusSlides(1)">❯</a>
                </div>
                <br>

                <div style="text-align:center">
                    {{SLIDER_DOTS}}
                </div>

                {{SLIDER_SCRIPT}}

                <!-- Order Form Section -->
                <section id="PBS-47z7f1661723616426" class="section page-section title-section PBS-47z7f1661723616426"
                    style="background: #fff;">
                    <div class="inner-container">
                        <div class="inner-title" data-pb-debug="margin padding">
                            <div class="fr-view">
                                <p class="title-section-content">
                                    <span class="title-section-stylish"></span>
                                    <a href="" style="font-size: 1.7rem; color: #2692c0;" target="_blank"
                                        class="title-section-text">
                                        {{ORDER_TEXT}}
                                    </a>
                                </p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Form Section -->
                <section id="PBS-qnim9k1632909988068"
                    class="section page-section express-checkout-form-section PBS-qnim9k1632909988068">
                    <div data-pb-debug="margin" class="inner-express-checkout-form">
                        <div data-pb-debug="padding" class="page-builder-express-checkout-wrapper">
                            <div class="fr-view">
                                <p><span style="font-size: 24px; color: #2692c0"><strong>
                                            <span class="offre_sale">عرض خاص !</span>
                                            <br>
                                            <span>
                                                {{OFFER_TEXT}}
                                            </span>
                                        </strong>
                                    </span>
                                </p>
                            </div>

                            <div class="product-price-container">
                                <span class="product-price">
                                    <span id="price_displayed" class="value">{{PRICE_1}}</span>
                                    <span class="currency">&nbsp;{{CURRENCY}}</span>
                                </span>
                            </div>
                            
                            <div id="express-checkout-section" class="product-section checkout-section">
                                <div class="checkout">
                                    <div class="main">
                                        <form method="post" enctype="multipart/form-data" id="formInfo">
                                            <input type="hidden" name="id" value="{{PRODUCT_ID}}">
                                            <input type="hidden" name="quantity" value="1">
                                            <input type="text" name="extra_payload" style="display: none;">
                                            
                                            <div class="checkout-form">
                                                <div class="checkout-groups">
                                                    <h2 class="checkout-heading"></h2>
                                                    {{FORM_FIELDS}}
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="product-section add-to-cart-section">
                                <button id="save_guest_order" style="font-size: 1.8rem;" type="submit"
                                    class="button single-submit" form="formInfo">
                                    <span>إضغط هنا لطلب المنتج</span>
                                    <i class="fa fa-spinner fa-spin" style="margin-right: 5px; display: none;"
                                        id="span_loading"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Body Images -->
                <div class="container">
                    {{BODY_IMAGES}}
                    
                    <a href="#PBS-qnim9k1632909988068">
                        <button id="fade_buy_btn_lead" class="overlay-button" style="top: 94%;
                        position: fixed;
                        padding: 15px 10px !important;
                        font-size: 21px;">إشتري الآن ب {{PRICE_1}} درهم فقط
                        </button>
                    </a>
                </div>

                <!-- Reviews Section -->
                <section id="PBS-lhtsfb1661723616426"
                    class="section page-section title-section PBS-lhtsfb1661723616426">
                    <div class="inner-container">
                        <div class="inner-title" data-pb-debug="margin padding">
                            <div class="fr-view">
                                <p class="title-section-content">
                                    <span class="title-section-stylish"></span>
                                    <a href="" target="_blank" class="title-section-text">
                                        أكثر من 750 زبون يثقون بنا
                                    </a>
                                </p>
                            </div>
                        </div>
                    </div>
                </section>

                <section class="u-align-left u-clearfix u-grey-5 u-typography-custom-page-typography-32--Testimonials u-section-1" id="carousel_d736">
                    <div class="u-clearfix u-sheet u-sheet-1">
                        <div class="u-expanded-width u-list u-list-1">
                            <div class="u-repeater u-repeater-1">
                                {{REVIEWS}}
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Countdown Section -->
                <section class="section page-section title-section" style="margin-top: 25px;">
                    <div class="inner-container countdown-container">
                        <h2 style="font-size: 21px; font-weight: 600;">تخفيض 50% و الشحن السريع بالمجان</h2>
                        <div id="countdown">
                            <div class="countdown-row">
                                <div class="countdown-column">
                                    <span class="countdown-label" id="seconds-label">ثانية</span>
                                    <span class="countdown-value" id="seconds-value"></span>
                                </div>
                                <div class="countdown-column">
                                    <span class="countdown-label" id="minutes-label">دقيقة</span>
                                    <span class="countdown-value" id="minutes-value"></span>
                                </div>
                                <div class="countdown-column">
                                    <span class="countdown-label" id="hours-label">ساعة</span>
                                    <span class="countdown-value" id="hours-value"></span>
                                </div>
                                <div class="countdown-column">
                                    <span class="countdown-label" id="days-label">يوم</span>
                                    <span class="countdown-value" id="days-value"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Guarantees Section -->
                {{GUARANTEES_SECTION}}

                <!-- FAQ Section -->
                {{FAQ_SECTION}}

                <!-- Footer -->
                <section class="section page-section title-section" style="background: #fff; padding-top: 25px; padding-bottom: 10px;">
                    <div class="inner-container">
                        <img src="images/logo.png" alt="" style="width: 80%; margin: auto;">
                        <h3 style="margin-top: 10px;">البريد الإلكتروني : <EMAIL></h3>
                        <div style="margin-top: 10px;display: flex; justify-content: center; flex-wrap: wrap; line-height: 3;">
                            <a href="pages/terms_of_use.html" target="_blank" style="margin: 0 0 0 4px;">شروط الاستخدام</a>|
                            <a href="pages/return_and_refund_policy.html" target="_blank" style="margin: 0 4px 0 4px;">سياسه الاستبدال والاسترجاع</a>|
                            <a href="pages/privacy_policy.html" target="_blank" style="margin: 0 4px 0 0;">سياسه الخصوصيه</a>
                        </div>
                    </div>
                </section>

                <footer style="padding: 13px 0; background: #2692c0; color: #fff;">
                    <h2 style="margin: 0;">جميع لحقوق محفوظة لدى 2023 © Moriny</h2>
                </footer>
            </div>
        </main>
    </div>

    {{PRICE_UPDATE_SCRIPT}}

    <script src="js/ycpay.js"></script>
    <script src="js/jquery-3.6.4.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/2.1.2/sweetalert.min.js"></script>
    <script src="js/countdown.js"></script>
    <script src="js/faq.js"></script>
    <script src="js/loarder_wrapper.js"></script>
    <script src="api_cozmo_landing_page.js"></script>
</body>
</html>
