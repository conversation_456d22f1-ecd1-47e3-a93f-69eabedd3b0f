<!DOCTYPE html>
<html dir="rtl">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="user-scalable=no, width=device-width, initial-scale=1, maximum-scale=1" />
    <meta name="theme-color" content="#FFF" />
    <meta name="msapplication-navbutton-color" content="#FFF" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="csrf-token" content="233G2Xj9p4pUeikewMtF9sgdnKzTDR772M94ZrfE">
    <title>shoes_womens</title>
    <meta property="og:title" content="shoes_womens" />
    <meta property="og:image" content="images/noxeva_lucci_logo.png" />

    <meta name="twitter:title" content="shoes_womens" />
    <meta name="twitter:description" content="" />
    <meta name="twitter:images0" content="images/noxeva_lucci_logo.png" />
    <link rel="icon" type="image/png" href="images/cozmo_lucci_favicon.png" />
    <!-- Google Fonts Link For Icons -->
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@48,400,0,0" />


    <link href="css/root.css" rel="stylesheet">
    <link href="css/store_font.css" rel="stylesheet">

    <!-- Facebook Pixel Code -->
    <!-- Meta Pixel Code -->
    <script>
        !function (f, b, e, v, n, t, s) {
            if (f.fbq) return; n = f.fbq = function () {
                n.callMethod ?
                n.callMethod.apply(n, arguments) : n.queue.push(arguments)
            };
            if (!f._fbq) f._fbq = n; n.push = n; n.loaded = !0; n.version = '2.0';
            n.queue = []; t = b.createElement(e); t.async = !0;
            t.src = v; s = b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t, s)
        }(window, document, 'script',
            'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', '1528998084725466');
        fbq('track', 'PageView');
    </script>
    <noscript><img height="1" width="1" style="display:none"
            src="https://www.facebook.com/tr?id=1528998084725466&ev=PageView&noscript=1" /></noscript>
    <!-- End Meta Pixel Code -->

    <!-- Tiktok Pixel Code -->
    <script>
        !function (w, d, t) {
            w.TiktokAnalyticsObject = t; var ttq = w[t] = w[t] || []; ttq.methods = ["page", "track", "identify", "instances", "debug", "on", "off", "once", "ready", "alias", "group", "enableCookie", "disableCookie"], ttq.setAndDefer = function (t, e) { t[e] = function () { t.push([e].concat(Array.prototype.slice.call(arguments, 0))) } }; for (var i = 0; i < ttq.methods.length; i++)ttq.setAndDefer(ttq, ttq.methods[i]); ttq.instance = function (t) { for (var e = ttq._i[t] || [], n = 0; n < ttq.methods.length; n++)ttq.setAndDefer(e, ttq.methods[n]); return e }, ttq.load = function (e, n) { var i = "https://analytics.tiktok.com/i18n/pixel/events.js"; ttq._i = ttq._i || {}, ttq._i[e] = [], ttq._i[e]._u = i, ttq._t = ttq._t || {}, ttq._t[e] = +new Date, ttq._o = ttq._o || {}, ttq._o[e] = n || {}; var o = document.createElement("script"); o.type = "text/javascript", o.async = !0, o.src = i + "?sdkid=" + e + "&lib=" + t; var a = document.getElementsByTagName("script")[0]; a.parentNode.insertBefore(o, a) };

            ttq.load('D0LPOCBC77UFC0GSO4S0');
            ttq.page();
        }(window, document, 'ttq');
    </script>
    <!-- Tiktok Pixel Code -->

    <script>
        // Disable right-click
        document.addEventListener('contextmenu', function (e) {
            e.preventDefault();
        });

        // Disable cut, copy, and paste
        document.addEventListener('cut', function (e) {
            e.preventDefault();
        });
        document.addEventListener('copy', function (e) {
            e.preventDefault();
        });
        document.addEventListener('paste', function (e) {
            e.preventDefault();
        });

        // Disable text selection
        document.addEventListener('selectstart', function (e) {
            e.preventDefault();
        });

        // Disable drag and drop
        document.addEventListener('dragstart', function (e) {
            e.preventDefault();
        });

        document.addEventListener('dragover', function (e) {
            e.preventDefault();
        });

        document.addEventListener('drop', function (e) {
            e.preventDefault();
        });

    </script>
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            var colorSelect = document.getElementById("product_color");
            var sizeSelect = document.getElementById("product_size");
            var orderButton = document.getElementById("save_guest_order");

            function checkAvailability() {
                var selectedColor = colorSelect.value;
                var selectedSize = sizeSelect.value;

                // Vérifiez si la couleur est "gris" et la taille est "M" ou "L"
                if (selectedColor === "gris" && (selectedSize === "M" || selectedSize === "L")) {
                    // Désactivez le bouton de commande et affichez un message
                    orderButton.disabled = true;
                    // colorSelect.value = "";
                    sizeSelect.value = "";

                    alert("اللون الرمادي غير متوفر في هذا المقاس. المرجو إختيار لون آخر.");
                } else {
                    // Activez le bouton de commande si la condition n'est pas remplie
                    orderButton.disabled = false;
                }
            }

            // Vérifiez l'indisponibilité au chargement de la page
            checkAvailability();

            // Ajoutez un événement "change" aux sélecteurs de couleur et de taille
            colorSelect.addEventListener("change", checkAvailability);
            sizeSelect.addEventListener("change", checkAvailability);
        });
    </script>



    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.4/css/all.css">
    <!--<link rel="stylesheet" href="css/fontawesome/all.css">-->
    <link rel="stylesheet" href="css/fontawesome/V4-shims.css">
    <link rel="stylesheet" href="css/style.css">



    <link rel="stylesheet" href="clients_reviews/nicepage.css" media="screen">
    <link rel="stylesheet" href="clients_reviews/Page-7.css" media="screen">

    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900;1000&family=Open+Sans:wght@600&display=swap');

        @font-face {
            font-family: 'Cairo';
            src: url('../fonts/cairo/cairo.ttf') format('truetype');
            /* font-weight: normal;
            font-style: normal; */
        }

        :root {
            font-size: 16px;
            text-align: center;
            font-family: 'Cairo', sans-serif !important;
        }

        * {
            font-family: 'Cairo', sans-serif !important;
        }

        body {
            color: #1a1a1a;
        }

        .column-child section {
            width: 100%;
        }

        @media (max-width: 425px) {
            :root {
                font-size: 12px;
            }
        }

        .page-builder>section {
            display: flex !important;
            justify-content: center;
        }

        body {
            background: #FFFFFF;
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;
        }

        .page-builder>section {
            max-width: 100%;
        }

        .page-builder>section {
            margin-top: 0px;
            margin-right: 0px;
            margin-bottom: 0px;
            margin-left: 0px;
        }


        .mySlides {
            display: none
        }

        img {
            vertical-align: middle;
        }

        /* Slideshow container */
        .slideshow-container {
            max-width: 1000px;
            position: relative;
            margin: auto;
        }

        /* Next & previous buttons */
        .prev,
        .next {
            cursor: pointer;
            position: absolute;
            top: 50%;
            left: 90%;
            width: auto;
            padding: 16px;
            margin-top: -22px;
            color: white;
            font-weight: bold;
            font-size: 18px;
            transition: 0.6s ease;
            border-radius: 0 3px 3px 0;
            user-select: none;
        }

        /* Position the "next button" to the right */
        .next {
            right: 90%;
            border-radius: 3px 0 0 3px;
        }


        /* On hover, add a black background color with a little bit see-through */
        .prev:hover,
        .next:hover {
            background-color: rgba(0, 0, 0, 0.8);
        }

        /* Caption text */
        .text {
            color: #f2f2f2;
            font-size: 15px;
            padding: 8px 12px;
            position: absolute;
            bottom: 8px;
            width: 100%;
            text-align: center;
        }

        /* Number text (1/3 etc) */
        .numbertext {
            color: #f2f2f2;
            font-size: 12px;
            padding: 8px 12px;
            position: absolute;
            top: 0;
        }

        /* The dots/bullets/indicators */
        .dot {
            cursor: pointer;
            height: 15px;
            width: 15px;
            margin: 0 2px;
            background-color: #bbb;
            border-radius: 50%;
            display: inline-block;
            transition: background-color 0.6s ease;
        }

        .active,
        .dot:hover {
            background-color: #717171;
        }

        /* Fading animation */
        .fade {
            animation-name: fade;
            animation-duration: 1.5s;
        }

        @keyframes fade {
            from {
                opacity: .4
            }

            to {
                opacity: 1
            }
        }

        /* On smaller screens, decrease text size */
        @media only screen and (max-width: 300px) {

            .prev,
            .next,
            .text {
                font-size: 11px
            }
        }

        /* Improved touch responsiveness */
        body {
            touch-action: pan-y;
        }
    </style>
    <script class="u-script" type="text/javascript" src="clients_reviews/jquery-1.9.1.min.js" defer=""></script>
    <script class="u-script" type="text/javascript" src="clients_reviews/nicepage.js" defer=""></script>
    <script src="js/slider1.js"></script>
    <link rel="stylesheet" href="css/loader.css">


</head>

<body class="body_fixed">
    <!-- <div id="loader-wrapper">
        <div id="loader"></div>
    </div> -->
    <div class="loader"></div>

    <!-- <div class="wrapper">
        <div class="box-wrap">
            <div class="box one"></div>
            <div class="box two"></div>
            <div class="box three"></div>
            <div class="box four"></div>
            <div class="box five"></div>
            <div class="box six"></div>
        </div>
    </div> -->

    <div id="app">
        <cart-side-summary
            :initial-cart="{&quot;sub_total&quot;:0,&quot;count&quot;:0,&quot;one_page_checkout&quot;:false,&quot;created_at&quot;:&quot;2023-04-16T17:52:26.299861Z&quot;,&quot;items&quot;:{&quot;data&quot;:[]}}"></cart-side-summary>
        <!-- App header -->
        <!-- Toast messages -->
        <flash></flash>
        <!-- App content -->
        <main class="page-wrapper">
            <div class="page-builder">
                <section id="PBS-ouwn1661723616425" class="section page-section column-section PBS-ouwn1661723616425">
                    <div class="inner-container" style="padding: 5px 0;">
                        <div class="inner-column" data-pb-debug="margin padding">
                            <div class="column-child">
                                <section id="PBS-svr6ws1661723616425"
                                    class="section page-section title-section PBS-svr6ws1661723616425">
                                    <div class="inner-container">
                                        <div class="inner-title" data-pb-debug="margin padding">
                                            <div class="fr-view">
                                                <p class="title-section-content">
                                                    <span class="title-section-stylish"></span>
                                                    <a href="" style="border: none; font-weight: 700; font-size: 17px;"
                                                        target="_blank" class="title-section-text"
                                                        data-pb-section="PBS-svr6ws1661723616425" data-pb-field="text"
                                                        data-pb-type="text" data-pb-value="ت 50% و توصي مجاني ريع">
                                                        تخفيض 50% و التوصيل مجاني و سريع
                                                    </a>
                                                </p>
                                            </div>
                                            <div class="subtitle-holder" data-pb-debug="margin"
                                                style="position: relative">
                                                <p class="subtitle-section-content"
                                                    data-pb-section="PBS-svr6ws1661723616425" data-pb-field="subtitle"
                                                    data-pb-type="text" data-pb-value="">

                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </section>


                            </div>
                        </div>
                    </div>
                </section>


                <div class="slideshow-container">

                    <div class="mySlides fade">
                        <div class="numbertext">1 / 3</div>
                        <img src="https://lucci-moriny.sirv.com/Images/shoes_womens/IMG-20250513-WA0002.jpg" style="width:100%">
                    </div>

                    <div class="mySlides fade">
                        <div class="numbertext">2 / 3</div>
                        <img src="https://lucci-moriny.sirv.com/Images/shoes_womens/IMG-20250513-WA0003.jpg" style="width:100%">
                    </div>

                    <div class="mySlides fade">
                        <div class="numbertext">3 / 3</div>
                        <img src="https://lucci-moriny.sirv.com/Images/shoes_womens/IMG-20250513-WA0011.jpg" style="width:100%">
                    </div>

                    <!-- <div class="mySlides fade">
                        <div class="numbertext">6 / 7</div>
                        <img src="images/05.jpg" style="width:100%">
                    </div>

                    <div class="mySlides fade">
                        <div class="numbertext">7 / 7</div>
                        <img src="images/06.jpg" style="width:100%">
                    </div> -->




                    <a class="prev" onclick="plusSlides(-1)">❮</a>
                    <a class="next" onclick="plusSlides(1)">❯</a>

                </div>
                <br>

                <div style="text-align:center">
                    <span class="dot" onclick="currentSlide(1)"></span>
                    <span class="dot" onclick="currentSlide(2)"></span>
                    <span class="dot" onclick="currentSlide(3)"></span>
                    <!-- <span class="dot" onclick="currentSlide(6)"></span>
                    <span class="dot" onclick="currentSlide(7)"></span> -->
                </div>

                <script>
                    let slideIndex = 1;
                    showSlides(slideIndex);

                    function plusSlides(n) {
                        showSlides(slideIndex += n);
                    }

                    function currentSlide(n) {
                        showSlides(slideIndex = n);
                    }

                    function showSlides(n) {
                        let i;
                        let slides = document.getElementsByClassName("mySlides");
                        let dots = document.getElementsByClassName("dot");
                        if (n > slides.length) { slideIndex = 1 }
                        if (n < 1) { slideIndex = slides.length }
                        for (i = 0; i < slides.length; i++) {
                            slides[i].style.display = "none";
                        }
                        for (i = 0; i < dots.length; i++) {
                            dots[i].className = dots[i].className.replace(" active", "");
                        }
                        slides[slideIndex - 1].style.display = "block";
                        dots[slideIndex - 1].className += " active";
                    }
                    // Auto-scroll functionality
                    let autoScrollInterval;

                    function startAutoScroll() {
                        autoScrollInterval = setInterval(function () {
                            plusSlides(1);
                        }, 3000); // Adjust the interval (in milliseconds) as needed
                    }

                    function stopAutoScroll() {
                        clearInterval(autoScrollInterval);
                    }

                    // Touch swipe functionality
                    let touchStartX;

                    document.addEventListener('touchstart', function (e) {
                        touchStartX = e.touches[0].clientX;
                        stopAutoScroll();
                    });

                    document.addEventListener('touchend', function (e) {
                        let touchEndX = e.changedTouches[0].clientX;
                        let swipeDistance = touchEndX - touchStartX;

                        if (swipeDistance > 50) {
                            plusSlides(-1);
                        } else if (swipeDistance < -50) {
                            plusSlides(1);
                        }

                        startAutoScroll();
                    });

                    // Start auto-scroll on page load
                    startAutoScroll();
                </script>

                <!-- <div class="container">
                    <img src="ihttps://lucci-moriny.sirv.com/Images/shoes_womens/shoes_womens.spin" alt="Image description">

                </div> -->



                <section id="PBS-47z7f1661723616426" class="section page-section title-section PBS-47z7f1661723616426"
                    style="background: #fff;">
                    <div class="inner-container">
                        <div class="inner-title" data-pb-debug="margin padding">
                            <div class="fr-view">
                                <p class="title-section-content">
                                    <span class="title-section-stylish"></span>
                                    <a href="" style="font-size: 1.7rem; color: #2692c0;" target="_blank"
                                        class="title-section-text" data-pb-section="PBS-47z7f1661723616426"
                                        data-pb-field="text" data-pb-type="text"
                                        data-pb-value="للطلب المرجو ملأ الإستمارة أسفله">
                                        للطلب المرجو ملٱ الإستمارة اسفله
                                    </a>
                                </p>
                            </div>
                        </div>
                    </div>
                </section>


                <section id="PBS-qnim9k1632909988068"
                    class="section page-section express-checkout-form-section PBS-qnim9k1632909988068">
                    <div data-pb-debug="margin" class="inner-express-checkout-form">
                        <div data-pb-debug="padding" class="page-builder-express-checkout-wrapper">
                            <div data-pb-section="PBS-qnim9k1632909988068" data-pb-field="express-checkout-form-header"
                                data-pb-type="html-editor" data-pb-value="فقط ب" class="fr-view">

                            </div>

                            <div data-pb-section="PBS-qnim9k1632909988068" data-pb-field="express-checkout-form-header"
                                data-pb-type="html-editor" data-pb-value="فقط ب" class="fr-view">
                                <p><span style="font-size: 24px; color: #2692c0"><strong>
                                            <span class="offre_sale">عرض خاص !</span>
                                            <br>
                                            <span>
                                                قميص واحد بسعر <span class="price_hot_sale_1">229 درهم</span> و قميصين
                                                بسعر <span class="price_hot_sale_1">399 درهم</span>
                                            </span>

                                        </strong>
                                    </span>
                                </p>
                            </div>


                            <div class="product-price-container"><span class="product-price"><span id="price_displayed"
                                        class="value">229</span><span class="currency">&nbsp;د.م</span></span>
                            </div>
                            <div id="express-checkout-section" class="product-section checkout-section"
                                data-pb-section="PBS-qnim9k1632909988068" data-pb-field="product-id"
                                data-pb-type="single-product" data-pb-value="[object Object]">
                                <div class="checkout">
                                    <div class="main">
                                        <form method="post" enctype="multipart/form-data" id="formInfo"><input
                                                type="hidden" name="id" value="cc83bb80-0d63-40cc-81f5-ba7093419793">
                                            <input type="hidden" name="quantity" value="1"> <input type="text"
                                                name="extra_payload" style="display: none;">
                                            <div class="checkout-form">
                                                <div class="checkout-groups">
                                                    <h2 class="checkout-heading"></h2>

                                                    <div class="form-group is-required"><label class="form-label">عدد
                                                            الطبقات</label>
                                                        <Select required
                                                            oninvalid="this.setCustomValidity('المرجو اختيار عدد الطبقات');"
                                                            onchange="try{setCustomValidity('')}catch(e){};"
                                                            x-moz-errormessage="المرجو اختيار عدد البقات" type="text"
                                                            name="tier_variante" id="tier_variante"
                                                            placeholder="عدد الطبقات">
                                                            <option value="">المرجو إختيار الكمية</option>
                                                            <option value="1">واحد ب 229 درهم فقط</option>
                                                            <option value="2">إثنان ب 399 درهم فقط</option>
                                                        </Select>
                                                    </div>

                                                    <div class="form-group is-required">


                                                        <label class="form-label">لون المتج</label>
                                                        <select required
                                                            oninvalid="this.setCustomValidity('المرجو اختيار لون المنتج');"
                                                            onchange="try{setCustomValidity('')}catch(e){};"
                                                            x-moz-errormessage="المرو اختيار لون المنتج" type="text"
                                                            name="product_color" id="product_color"
                                                            placeholder="لون المنتج">
                                                            <option value="">المرجو إختيار لون المنتج</option>
                                                            <option value="dark">أسود</option>
                                                            <option value="brown">بني</option>
                                                            <option value="white">أبيض</option>
                                                            <!-- <option value="green">أخضر</option> -->
                                                            <!-- Add more color options as needed -->
                                                        </select>
                                                    </div>



                                                    <div class="form-group is-required">
                                                        <label class="form-label">الحجم</label>
                                                        <select required
                                                            oninvalid="this.setCustomValidity('المرجو اختيار الحجم');"
                                                            onchange="try{setCustomValidity('')}catch(e){};"
                                                            x-moz-errormessage="المرجو اختيار الحجم" name="product_size"
                                                            id="product_size" placeholder="الحجم">
                                                            <option value="">المرجو إختيار الحجم</option>
                                                            <option value="35">35</option>
                                                            <option value="36">36</option>  
                                                            <option value="37">37</option>
                                                            <option value="38">38</option>
                                                            <option value="39">39</option>
                                                            <option value="40">40</option>
                                                            <option value="41">41</option>  
                                                            <option value="42">42</option>
                                                            <option value="43">43</option>
                                                            <option value="44">44</option>
                                                            <!-- <option value="XXL">XXL</option> -->
                                                            <!-- Add more sizes options as needed -->
                                                        </select>
                                                    </div>

                                                    <!-- </div> -->
                                                    <input type="hidden" name="number_tier" id="number_tier" value="1">
                                                    <input type="hidden" name="price_tiers" id="price_tiers" value="">
                                                    <div class="form-group is-required"><label class="form-label">الإسم
                                                            الكامل
                                                        </label> <input required
                                                            oninvalid="this.setCustomValidity('المرجو إخال إسمك');"
                                                            onchange="try{setCustomValidity('')}catch(e){};"
                                                            x-moz-errormessage="المرجو دخال إسمك" type="text"
                                                            name="fullname" placeholder="الأسم الكامل">
                                                    </div>
                                                    <div class="form-group is-required"><label class="form-label">رقم
                                                            الهاتف</label> <input required
                                                            oninvalid="this.setCustomValidity('المرجو إدخال قم الهاتف');"
                                                            onchange="try{setCustomValidity('')}catch(e){};"
                                                            x-moz-errormessage="المرجو إدخال رقم الهاتف" type="number"
                                                            name="phone" placeholder="رقم الهاتف"></div>
                                                    <div class="form-group is-required"><label
                                                            class="form-label">العنوان</label> <input required
                                                            oninvalid="this.setCustomValidity('المرجو إدخال العنوان');"
                                                            onchange="try{setCustomValidity('')}catch(e){};"
                                                            x-moz-errormessage="المرجو إدخال العنوان" type="text"
                                                            name="adresse" placeholder="العنوان"></div>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div data-pb-section="PBS-qnim9k1632909988068"
                                data-pb-field="express-checkout-form-pre-footer" data-pb-type="html-editor"
                                data-pb-value="" class="fr-view ec-form-pre-footer"></div>
                            <div class="product-section add-to-cart-section">
                                <button id="save_guest_order" style="font-size: 1.8rem;" type="submit"
                                    data-pb-section="PBS-qnim9k1632909988068" data-pb-field="button-text"
                                    data-pb-type="text" data-pb-value="إضغط هنا لطلب المنتج"
                                    class="button single-submit" form="formInfo">
                                    <span>إضغط هنا لطلب المنتج</span>
                                    <i class="fa fa-spinner fa-spin" style="margin-right: 5px; display: none;"
                                        id="span_loading"></i>
                                </button>
                            </div>
                            <div data-pb-section="PBS-qnim9k1632909988068" data-pb-field="express-checkout-form-footer"
                                data-pb-type="html-editor" data-pb-value="" class="fr-view ec-form-footer"></div>
                        </div>
                    </div>
                </section>

                <div class="container">
                    <img src="https://lucci-moriny.sirv.com/Images/shoes_womens/new/IMG-20250513-WA0011.jpg" alt="">
                    <img src="https://lucci-moriny.sirv.com/Images/shoes_womens/IMG-20250513-WA0008.jpg" alt="">
                    <img src="https://lucci-moriny.sirv.com/Images/shoes_womens/IMG-20250513-WA0009.jpg" alt="">
                    <img src="https://lucci-moriny.sirv.com/Images/shoes_womens/IMG-20250513-WA0012.jpg" alt="">
                    <img src="https://lucci-moriny.sirv.com/Images/shoes_womens/IMG-20250513-WA0014.jpg" alt="">
                    <img src="https://lucci-moriny.sirv.com/Images/shoes_womens/IMG-20250513-WA0014.jpg" alt="">
                    <img src="https://lucci-moriny.sirv.com/Images/shoes_womens/IMG-20250513-WA0015.jpg" alt="">
                    

                    <img src="https://d2m3z4bu5nao69.cloudfront.net/a652d26a2f6b47d9bdb0e3aed7d36573.gif?x-oss-process=webp" alt="">
                    <img src="https://d2m3z4bu5nao69.cloudfront.net/fc8cb2ed4f2a4afebcd16f0e597eaa43.png?x-oss-process=webp" alt="">
                    <img src="https://d2m3z4bu5nao69.cloudfront.net/ed6c3e6ae7ed44e8a52e22e66f9dd6b6.gif?x-oss-process=webp" alt="">
                    <img src="https://d2m3z4bu5nao69.cloudfront.net/80dfc5dedb624d1fb16c395010f813e7.jpg?x-oss-process=webp" alt="">
                    
                    <!-- <img src="https://cdn.shopify.com/s/files/1/0823/3821/8305/files/pant17.jpg?v=1703286877" alt=""> -->

                    <!-- <a href="#PBS-qnim9k1632909988068">
                        <button id="fade_buy_btn_lead" class="overlay-button" style="top: 5.5%;">إشتري الآن ب 229 درهم فقط
                            </button>
                    </a> -->
                    <a href="#PBS-qnim9k1632909988068">
                        <button id="fade_buy_btn_lead" class="overlay-button" style="top: 94%;
                        position: fixed;
                        padding: 15px 10px !important;
                        font-size: 21px;    ">إشتري الآن ب 229 درهم فقط
                        </button>
                    </a>
                </div>

                <section id="PBS-lhtsfb1661723616426"
                    class="section page-section title-section PBS-lhtsfb1661723616426">
                    <div class="inner-container">
                        <div class="inner-title" data-pb-debug="margin padding">
                            <div class="fr-view">
                                <p class="title-section-content">
                                    <span class="title-section-stylish"></span>
                                    <a href="" target="_blank" class="title-section-text"
                                        data-pb-section="PBS-lhtsfb1661723616426" data-pb-field="text"
                                        data-pb-type="text" data-pb-value="أكثر من 750 زبون يثقون بنا">
                                        أكثر من 750 زبون يثقون بنا
                                    </a>
                                </p>
                            </div>
                            <div class="subtitle-holder" data-pb-debug="margin" style="position: relative">
                                <p class="subtitle-section-content" data-pb-section="PBS-lhtsfb1661723616426"
                                    data-pb-field="subtitle" data-pb-type="text" data-pb-value="">

                                </p>
                            </div>
                        </div>
                    </div>
                </section>


                <section
                    class="u-align-left u-clearfix u-grey-5 u-typography-custom-page-typography-32--Testimonials u-section-1"
                    id="carousel_d736">
                    <div class="u-clearfix u-sheet u-sheet-1">
                        <div class="u-expanded-width u-list u-list-1">
                            <div class="u-repeater u-repeater-1">
                                <div class="u-align-right u-container-style u-list-item u-repeater-item">
                                    <div
                                        class="u-container-layout u-similar-container u-valign-middle-md u-container-layout-1">
                                        <div
                                            class="u-container-style u-group u-radius-20 u-shape-round u-similar-fill u-white u-group-1">
                                            <div class="u-container-layout u-valign-middle u-container-layout-2">
                                                <p class="u-align-left u-text u-text-black u-text-font u-text-2">طلبت
                                                    واحد وصلني اليوم زوين بزاف و مريح
                                                </p>
                                                <h5
                                                    class="u-align-right u-custom-font u-heading-font u-text u-text-black u-text-3">
                                                    -  أميرة                                               </h5>
                                            </div>
                                        </div>
                                        <div class="u-image u-image-circle u-preserve-proportions u-image-1" alt=""
                                            data-image-width="1265" data-image-height="1080"></div>
                                    </div>
                                </div>
                                <div class="u-align-right u-container-style u-list-item u-repeater-item">
                                    <div
                                        class="u-container-layout u-similar-container u-valign-middle-md u-container-layout-3">
                                        <div
                                            class="u-container-style u-group u-radius-20 u-shape-round u-similar-fill u-white u-group-2">
                                            <div class="u-container-layout u-valign-middle u-container-layout-4">
                                                <p class="u-align-left u-text u-text-black u-text-font u-text-4">
                                                    طلبت واحد للزوجة ديالي, وصلني اليوم زوين بزاف و مريح

                                                </p>
                                                <h5
                                                    class="u-align-right u-custom-font u-heading-font u-text u-text-black u-text-5">
                                                    - Mohammed Ka</h5>
                                            </div>
                                        </div>
                                        <div class="u-image u-image-circle u-preserve-proportions u-image-2" alt=""
                                            data-image-width="720" data-image-height="1080"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="u-repeater u-repeater-2">
                                <div class="u-align-right u-container-style u-list-item u-repeater-item">
                                    <div
                                        class="u-container-layout u-similar-container u-valign-middle-md u-container-layout-3">
                                        <div
                                            class="u-container-style u-group u-radius-20 u-shape-round u-similar-fill u-white u-group-2">
                                            <div class="u-container-layout u-valign-middle u-container-layout-4">
                                                <p class="u-align-left u-text u-text-black u-text-font u-text-4">تبار
                                                    الله عليكم منتوج في المستوى، الجوة عالية، التوصيل سريع. شكرا لكم


                                                </p>
                                                <h5
                                                    class="u-align-right u-custom-font u-heading-font u-text u-text-black u-text-5">
                                                    - Kamal</h5>
                                            </div>
                                        </div>
                                        <div class="u-image u-image-circle u-preserve-proportions u-image-3" alt=""
                                            data-image-width="720" data-image-height="1080"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>


                <section class="section page-section title-section" style="margin-top: 25px;">
                    <div class="inner-container countdown-container">

                        <h2 style="font-size: 21px; font-weight: 600;">تخفيض 50% و الشحن السريع بالمجان</h2>
                        <div id="countdown">
                            <div class="countdown-row">
                                <div class="countdown-column">
                                    <span class="countdown-label" id="seconds-label">ثانية</span>
                                    <span class="countdown-value" id="seconds-value"></span>
                                </div>
                                <div class="countdown-column">
                                    <span class="countdown-label" id="minutes-label">دقيقة</span>
                                    <span class="countdown-value" id="minutes-value"></span>
                                </div>
                                <div class="countdown-column">
                                    <span class="countdown-label" id="hours-label">ساعة</span>
                                    <span class="countdown-value" id="hours-value"></span>
                                </div>
                                <div class="countdown-column">
                                    <span class="countdown-label" id="days-label">يوم</span>
                                    <span class="countdown-value" id="days-value"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>


                <section class="section page-section title-section"
                    style="margin-top: 40px; background: linear-gradient(to bottom, #f1f0ee, #ffffff, #ffffff, #ffffff) !important;">
                    <div class="inner-container">
                        <!-- <h1 style="font-weight: 700;font-size: 2.5rem; margin-bottom: 45px;">الضمانات و الاسترجاع</h1> -->

                        <p class="title-section-content" style="margin-bottom: 60px;">
                            <span class="title-section-stylish"></span>
                            <a href="" target="_blank" class="title-section-text"
                                data-pb-section="PBS-lhtsfb1661723616426" data-pb-field="text" data-pb-type="text"
                                data-pb-value="الضمانات و الاسترجاع">
                                الضمانات و الإسترجاع
                            </a>
                        </p>
                        <div class="guaranties-column">
                            <img style="width: 18%; margin: auto;" src="images/icons/costumer-service.png" alt="">
                            <h2 style="font-weight: 700; font-size: 20px;">خدمة ما بعد البيع</h2>
                            <p style="font-size: 17px; font-weight: 500;">خدمة العملاء متوفرة طيلة أيام الأسبوع
                            </p>
                        </div>
                        <div class="guaranties-column">
                            <img style="width: 18%; margin: auto;" src="images/icons/delivery.png" alt="">
                            <h2 style="font-weight: 700; font-size: 20px;">الشحن مجاني</h2>
                            <p style="font-size: 17px; font-weight: 500;">التوصيل سريع و مجاني لجميع أنحاء المغرب
                            </p>
                        </div>
                        <div class="guaranties-column">
                            <img style="width: 18%; margin: auto;" src="images/icons/money.png" alt="">
                            <h2 style="font-weight: 700; font-size: 20px;">الدفع عند الاستلام</h2>
                            <p style="font-size: 17px; font-weight: 500;">لن تدفع اي شي حتى تتوصل بالمنتج
                            </p>
                        </div>
                        <div class="guaranties-column">
                            <img style="width: 18%; margin: auto;" src="images/icons/bird.png" alt="">
                            <h2 style="font-weight: 700; font-size: 20px;">ضمان المنتج</h2>
                            <p style="font-size: 17px; font-weight: 500;">ضمان 10 أيام إذا لم يعجبك يمكنك
                                استرداد أموالك
                            </p>
                        </div>
                    </div>
                </section>

                <section class="section page-section title-section"
                    style="background: linear-gradient(to bottom, #fff, #d0dee7, #d0dee7, #fff) !important; padding-bottom: 20px;">
                    <div class="faq-container">
                        <!-- <h1 style="font-weight: 700;font-size: 2.5rem; margin-bottom: 45px;">الاسئلة الشائعة حول النتج -->
                        </h1>
                        <p class="title-section-content"
                            style="text-align: center; font-size: 1.4rem; margin-bottom: 45px;">
                            <span class="title-section-stylish"></span>
                            <a href="" target="_blank" class="title-section-text"
                                data-pb-section="PBS-lhtsfb1661723616426" data-pb-field="text" data-pb-type="text"
                                data-pb-value="الاسئلة الشائعة حول المنتج">
                                الاسئلة الشائعة حول المتج
                            </a>
                        </p>
                        <div class="question">هل تقبلون الدفع عند الإستلام؟</div>
                        <div class="answer">نعم، نحن نقبل الدفع عند الإستلام</div>

                        <div class="question">كم فترة التوصيل؟</div>
                        <div class="answer">فترة التوصيل تعتمد على المدينة، و عادة ما تكون بين 1 و 3 أيام عمل
                        </div>

                        <div class="question">هل يمكن إرجاع المنتج إذا لم يعجبني؟</div>
                        <div class="answer">نعم، يمكنك إرجاع المنتج ف حالة عدم الرضا عه في غضون 10 أيام من تاريخ
                            الشراء وبشر أن يكون في حالته الأصلية</div>

                        <div class="question">هل يوجد ضمان عل المنتج؟</div>
                        <div class="answer">نعم، ضمان المنتج يشمل عيوب التصنيع و تختلف مدة الضمان حسب المنتج</div>
                    </div>
                </section>





                <section class="section page-section title-section"
                    style="background: #fff; padding-top: 25px; padding-bottom: 10px;">
                    <div class="inner-container">
                        <img src="images/noxeva_lucci_logo.png" alt="" style="width: 80%; margin: auto;">
                        <h3 style="margin-top: 10px;">البريد الإلكتروني : <EMAIL></h3>
                        <div
                            style="margin-top: 10px;display: flex; justify-content: center; flex-wrap: wrap; line-height: 3;">
                            <a href="pages/terms_of_use.html" target="_blank" style="margin: 0 0 0 4px;">شروط
                                الاستخدام</a>|
                            <a href="pages/return_and_refund_policy.html" target="_blank"
                                style="margin: 0 4px 0 4px;">سياسه الاستبدال
                                والاسترجاع</a>|
                            <a href="pages/privacy_policy.html" target="_blank" style="margin: 0 4px 0 0;">سياسه
                                الخصوصيه</a>
                        </div>
                    </div>
                </section>

                <footer style="padding: 13px 0; background: #2692c0; color: #fff;">
                    <h2 style="margin: 0;">جميع لحقوق محفوظة لدى 2023 © Moriny</h2>
                </footer>

            </div>



        </main>
        <!-- App footer -->
        <!-- Hookables -->
    </div>

    <script>

        var price_tiers = document.getElementById("price_tiers");
        var selectElement = document.getElementById("tier_variante");
        // var hiddenInput = document.getElementById("quantity");
        var currencyElement = document.getElementById("price_displayed");


        selectElement.addEventListener("change", function () {

            var selectedValue = selectElement.value;

            if (selectedValue === "1") {
                price_tiers.value = "229";
                // hiddenInput.value = "1";
                currencyElement.innerText = "229";
            } else if (selectedValue === "2") {
                price_tiers.value = "399";
                // hiddenInput.value = "2";
                currencyElement.innerText = "399";
            } else {
                price_tiers.value = "229";
                // hiddenInput.value = "1";
                currencyElement.innerText = "229";
            }
        });


    </script>

    <script src="js/ycpay.js"></script>
    <script src="js/jquery-3.6.4.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/2.1.2/sweetalert.min.js"
        integrity="sha512-AA1Bzp5Q0K1KanKKmvN/4d3IRKVlv9PYgwFPvm32nPO6QS8yH1HO7LbgB1pgiOxPtfeg5zEn2ba64MUcqJx6CA=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="api_cozmo_landing_page.js"></script>
    <script src="js/countdown.js"></script>
    <script src="js/faq.js"></script>
    <script src="js/loarder_wrapper.js"></script>

</body>

</html>