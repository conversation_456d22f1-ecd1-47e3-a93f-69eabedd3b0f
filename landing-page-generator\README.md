# Landing Page Generator Dashboard

## نظرة عامة

هذا النظام عبارة عن لوحة تحكم لتوليد صفحات الهبوط (Landing Pages) بشكل تلقائي. يمكنك من خلاله إنشاء صفحات هبوط مخصصة لمنتجاتك المختلفة دون الحاجة لكتابة الكود يدوياً.

## المتطلبات

- PHP 7.4 أو أحدث
- SQLite (مدمج مع PHP)
- استضافة مشتركة تدعم PHP

## التثبيت

1. ارفع جميع الملفات إلى مجلد في الاستضافة المشتركة
2. تأكد من أن مجلد `data` قابل للكتابة (permissions 755)
3. تأكد من أن مجلد `generated` قابل للكتابة (permissions 755)
4. افتح الرابط في المتصفح

## الميزات

### 1. إدارة المحتوى والأسعار
- تخصيص العناوين والنصوص
- تحديد الأسعار والعملة
- إضافة عروض خاصة

### 2. إعدادات البكسل
- Facebook Pixel ID
- TikTok Pixel ID
- تتبع التحويلات تلقائياً

### 3. إدارة الصور
- صور السلايدر (عرض تقديمي)
- صور المحتوى
- دعم الروابط المباشرة أو رفع الملفات

### 4. نظام النماذج المرن
- **عينة**: اسم، عنوان، هاتف، عرض
- **متغير واحد**: إضافة خيارات اللون
- **متغيرين**: إضافة خيارات اللون والحجم
- إمكانية إضافة حقل العرض/الهدية

### 5. إدارة التقييمات
- مكتبة تقييمات جاهزة
- إمكانية اختيار التقييمات المناسبة
- اقتراحات تلقائية

### 6. إعدادات API
- تكامل مع SheetDB
- تخصيص تنسيق البيانات
- إعدادات مخصصة لكل منتج

## كيفية الاستخدام

### إنشاء صفحة هبوط جديدة

1. **المعلومات الأساسية**
   - أدخل اسم المشروع
   - أدخل عنوان الصفحة

2. **المحتوى والأسعار**
   - اكتب العنوان الرئيسي
   - حدد نص الطلب
   - أدخل الأسعار والعملة

3. **إعدادات البكسل**
   - أدخل Facebook Pixel ID
   - أدخل TikTok Pixel ID

4. **الصور**
   - أضف صور السلايدر (بالترتيب)
   - أضف صور المحتوى
   - يمكن استخدام روابط مباشرة أو رفع ملفات

5. **النموذج**
   - اختر نوع المنتج
   - حدد إذا كان يحتوي على عرض
   - أضف خيارات الألوان والأحجام

6. **التقييمات**
   - اختر التقييمات المناسبة من المكتبة
   - أو استخدم الاقتراحات التلقائية

7. **إعدادات API**
   - أدخل SheetDB API Key
   - أدخل Authorization Token
   - خصص تنسيق البيانات (اختياري)

### حفظ وتوليد الصفحة

1. **حفظ الإعدادات**: احفظ التكوين للتعديل لاحقاً
2. **معاينة**: شاهد كيف ستبدو الصفحة
3. **توليد وتحميل**: احصل على ملف ZIP يحتوي على الصفحة كاملة

## هيكل الملفات المولدة

```
project-name/
├── index.html                 # الصفحة الرئيسية
├── api_cozmo_landing_page.js  # ملف API المخصص
├── css/                       # ملفات التنسيق
├── js/                        # ملفات JavaScript
├── images/                    # الصور
├── pages/                     # صفحات السياسات
├── clients_reviews/           # ملفات التقييمات
├── loader.html               # صفحة التحميل
└── order_success.html        # صفحة نجاح الطلب
```

## إعدادات SheetDB

1. أنشئ حساب على [SheetDB.io](https://sheetdb.io)
2. أنشئ جدول بيانات جديد
3. احصل على API Key
4. احصل على Authorization Token
5. أدخل البيانات في إعدادات API

### تنسيق البيانات الافتراضي

```javascript
{
    name: "product_name",
    date: "current_date",
    customer_name: "customer_name",
    phone: "phone_number",
    city: "-",
    address: "customer_address",
    quantity: "selected_quantity",
    price: "total_price",
    product_notice: "",
    notice: "Color: selected_color",
    status: "pending",
    fees_shipping: "",
    size: "selected_size"
}
```

## نصائح للاستخدام

### الصور
- استخدم صور عالية الجودة
- تأكد من أن الصور محسنة للويب
- استخدم تنسيقات JPG أو PNG
- حجم مناسب: 800x600 بكسل أو أكبر

### النصوص
- اكتب نصوص واضحة ومقنعة
- استخدم اللغة العربية الصحيحة
- تأكد من صحة الأسعار

### التقييمات
- اختر تقييمات متنوعة
- تأكد من أنها مناسبة للمنتج
- لا تستخدم أكثر من 5 تقييمات

## الدعم الفني

إذا واجهت أي مشاكل:

1. تأكد من أن PHP يعمل بشكل صحيح
2. تحقق من صلاحيات المجلدات
3. تأكد من أن SQLite مفعل
4. راجع ملف الأخطاء في الاستضافة

## التحديثات المستقبلية

- إضافة قوالب جديدة
- تحسين واجهة المستخدم
- إضافة المزيد من خيارات التخصيص
- تكامل مع منصات أخرى

## الأمان

- تأكد من حماية مجلد البيانات
- لا تشارك معلومات API
- استخدم كلمات مرور قوية للاستضافة
- قم بعمل نسخ احتياطية دورية

---

**ملاحظة**: هذا النظام مصمم للاستخدام على الاستضافة المشتركة ولا يتطلب VPS أو خادم مخصص.
